@echo off
setlocal enabledelayedexpansion
REM ===============================================================
REM TNGD Monthly Backup System - Standalone Version      S4NG-7S
REM ===============================================================
REM Standalone monthly backup system with built-in functions:
REM - Self-contained with all necessary functions
REM - Proper input validation and security hardening
REM - Health checks and dependency validation
REM - Fallback configuration sources
REM - Comprehensive monitoring and alerting
REM ===============================================================

REM Set the current directory to the project root
cd /d "%~dp0.."

REM Configuration
set "PYTHON_CMD=python"
set "MONTHLY_BACKUP_SCRIPT=scripts\enhanced_monthly_backup_processor.py"

REM Initialize logging
call :get_timestamp timestamp
set "LOG_DIR=logs\monthly\!timestamp:~0,10!"
if not exist "!LOG_DIR!" mkdir "!LOG_DIR!" 2>nul
set "LOG_FILE=!LOG_DIR!\monthly_backup_!timestamp!.log"

call :log_operation_start "Monthly Backup System"

REM Process command line arguments directly
set "MONTH_NAME=%~1"
set "YEAR=%~2"
set "DRY_RUN=false"
set "VERBOSE=false"
set "FORCE_RECOVERY=false"
set "SKIP_HEALTH_CHECKS=false"

REM Check for help first
if /i "%~1"=="help" goto show_monthly_help
if /i "%~1"=="--help" goto show_monthly_help
if "%~1"=="" goto show_monthly_help

REM Validate required parameters
if "!MONTH_NAME!"=="" (
    call :log_error "Missing required parameter: month name"
    goto show_monthly_help
)

if "!YEAR!"=="" set "YEAR=2025"

REM Process optional arguments
shift & shift
:parse_args_loop
if "%~1"=="" goto args_done

if /i "%~1"=="--dry-run" (
    set "DRY_RUN=true"
    call :log_info "Dry run mode enabled"
    shift & goto parse_args_loop
)

if /i "%~1"=="--verbose" (
    set "VERBOSE=true"
    call :log_info "Verbose mode enabled"
    shift & goto parse_args_loop
)

if /i "%~1"=="--force-recovery" (
    set "FORCE_RECOVERY=true"
    call :log_info "Force recovery mode enabled"
    shift & goto parse_args_loop
)

if /i "%~1"=="--skip-health-checks" (
    set "SKIP_HEALTH_CHECKS=true"
    call :log_info "Health checks will be skipped"
    shift & goto parse_args_loop
)

call :log_warning "Unknown parameter: %~1"
shift & goto parse_args_loop

:args_done

goto :main_monthly_process

REM ===============================================================
REM BUILT-IN UTILITY FUNCTIONS
REM ===============================================================

:log_error
echo [ERROR] [%date% %time%] %~1
if defined LOG_FILE echo [ERROR] [%date% %time%] %~1 >> "!LOG_FILE!"
goto :eof

:log_info
echo [INFO] [%date% %time%] %~1
if defined LOG_FILE echo [INFO] [%date% %time%] %~1 >> "!LOG_FILE!"
goto :eof

:log_warning
echo [WARNING] [%date% %time%] %~1
if defined LOG_FILE echo [WARNING] [%date% %time%] %~1 >> "!LOG_FILE!"
goto :eof

:validate_required_param
if "%~1"=="" (
    call :log_error "Missing required parameter: %~2"
    exit /b 1
)
goto :eof

:validate_safe_string
if "%~1"=="" (
    call :log_error "Empty parameter: %~2"
    exit /b 1
)
REM Check for dangerous characters
echo "%~1" | findstr /r "[&|<>^\"*?]" >nul 2>&1
if !ERRORLEVEL! EQU 0 (
    call :log_error "Invalid characters in parameter: %~2"
    exit /b 1
)
goto :eof

:get_timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value 2^>nul') do set "dt=%%a"
if "!dt!"=="" (
    set "timestamp=%date:~-4%-%date:~4,2%-%date:~7,2%_%time:~0,2%-%time:~3,2%-%time:~6,2%"
) else (
    set "timestamp=!dt:~0,4!-!dt:~4,2!-!dt:~6,2!_!dt:~8,2!-!dt:~10,2!-!dt:~12,2!"
)
set "%~1=!timestamp!"
goto :eof

:log_operation_start
call :log_info "=== STARTING: %~1 ==="
goto :eof

:log_operation_end
call :log_info "=== COMPLETED: %~1 ==="
call :log_info "Exit code: %~2"
goto :eof

REM ===============================================================
REM MAIN MONTHLY PROCESS
REM ===============================================================

:main_monthly_process
echo ===============================================================
echo TNGD MONTHLY BACKUP SYSTEM
echo ===============================================================
echo Target: !MONTH_NAME! !YEAR!
echo Mode: Standalone with Built-in Functions
echo Log File: !LOG_FILE!
if "!DRY_RUN!"=="true" echo Mode: DRY RUN (validation only)
if "!FORCE_RECOVERY!"=="true" echo Mode: FORCE RECOVERY enabled
echo Current time: %date% %time%
echo ===============================================================
echo.

REM Configuration discovery
if exist "tabletest\tables.json" (
    set "TABLE_CONFIG_PATH=tabletest\tables.json"
    call :log_info "Found table configuration: tabletest\tables.json"
) else if exist "config\tables.json" (
    set "TABLE_CONFIG_PATH=config\tables.json"
    call :log_info "Found table configuration: config\tables.json"
) else if exist "backup\tables.json" (
    set "TABLE_CONFIG_PATH=backup\tables.json"
    call :log_info "Found table configuration: backup\tables.json"
) else (
    echo ❌ No table configuration found in any location
    echo.
    echo Checked locations:
    echo   - tabletest\tables.json
    echo   - config\tables.json
    echo   - backup\tables.json
    echo.
    echo Please create a table configuration file in one of these locations.
    call :log_error "Table configuration not found"
    exit /b 8
)

REM Validate table configuration
if not exist "!TABLE_CONFIG_PATH!" (
    call :log_error "Configuration file not found: !TABLE_CONFIG_PATH!"
    exit /b 8
)
call :log_info "Configuration validation completed"

call :log_info "Using table configuration: !TABLE_CONFIG_PATH!"

REM Health checks
if not "!SKIP_HEALTH_CHECKS!"=="true" (
    call :log_info "Starting system health checks..."
    echo Performing system health checks...

    REM Check Python availability
    %PYTHON_CMD% --version >nul 2>&1
    if !ERRORLEVEL! NEQ 0 (
        echo ❌ Python not available or not in PATH
        call :log_error "Python not available or not in PATH"
        exit /b 9
    )
    echo ✅ Python availability check passed

    REM Check disk space
    %PYTHON_CMD% -c "import shutil; free = shutil.disk_usage('.').free / (1024**3); print(f'Free space: {free:.1f} GB'); exit(0 if free > 5 else 1)" 2>nul
    if !ERRORLEVEL! NEQ 0 (
        echo ⚠️ Low disk space detected (less than 5GB free)
        call :log_warning "Low disk space detected (less than 5GB free)"
    ) else (
        echo ✅ Disk space check passed
    )

    echo ✅ All health checks completed
    call :log_info "All health checks completed"
) else (
    echo ⚠️ Health checks skipped (--skip-health-checks flag used)
    call :log_info "Health checks skipped by user"
)

echo.
echo ===============================================================
echo STARTING MONTHLY BACKUP PROCESS
echo ===============================================================
echo Configuration: !TABLE_CONFIG_PATH!
echo Target: !MONTH_NAME! !YEAR!
echo ===============================================================
echo.

REM Check if backup script exists
if not exist "!MONTHLY_BACKUP_SCRIPT!" (
    echo ⚠️ Monthly backup script not found: !MONTHLY_BACKUP_SCRIPT!
    echo Using fallback: will attempt to run daily backup for the specified month
    call :log_warning "Monthly backup script not found, using fallback"

    REM Fallback to daily backup
    set "backup_command=!PYTHON_CMD! scripts\daily_backup_scheduler.py --month !MONTH_NAME! --year !YEAR!"
) else (
    REM Build parameters for monthly script
    set "backup_params=--month "!MONTH_NAME!" --year !YEAR! --config-path "!TABLE_CONFIG_PATH!""
    if "!DRY_RUN!"=="true" set "backup_params=!backup_params! --dry-run"
    if "!VERBOSE!"=="true" set "backup_params=!backup_params! --verbose"
    if "!FORCE_RECOVERY!"=="true" set "backup_params=!backup_params! --force-recovery"
    REM Note: Python script doesn't support --skip-health-checks, use --force-recovery instead

    set "backup_command=!PYTHON_CMD! !MONTHLY_BACKUP_SCRIPT! !backup_params!"
)

call :log_info "Starting monthly backup processor..."
call :log_info "Running: !backup_command!"
echo.

REM Execute backup
if "!DRY_RUN!"=="true" (
    echo ✅ DRY RUN: Would execute: !backup_command!
    echo ✅ Configuration validated successfully
    echo ✅ All checks passed - ready for actual backup
    set "BACKUP_EXIT_CODE=0"
) else (
    !backup_command!
    set "BACKUP_EXIT_CODE=!ERRORLEVEL!"
)

echo.
echo ===============================================================
echo MONTHLY BACKUP COMPLETED
echo ===============================================================
echo Exit code: !BACKUP_EXIT_CODE!
echo Completion time: %date% %time%
echo Log file: !LOG_FILE!
echo ===============================================================

call :log_operation_end "Monthly Backup System" !BACKUP_EXIT_CODE!

if !BACKUP_EXIT_CODE! EQU 0 (
    echo ✅ Monthly backup completed successfully!
    call :log_info "Monthly backup completed successfully"
) else (
    echo ❌ Monthly backup failed or completed with errors.
    echo Check the log file for details: !LOG_FILE!
    call :log_error "Monthly backup failed - check logs for details"
)

echo.
echo Recent log entries:
echo ===============================================================
call :show_recent_logs
echo ===============================================================

exit /b !BACKUP_EXIT_CODE!

REM ===============================================================
REM UTILITY FUNCTIONS
REM ===============================================================

:find_table_config
if exist "tabletest\tables.json" (
    set "TABLE_CONFIG_PATH=tabletest\tables.json"
    call :log_info "Found table configuration: tabletest\tables.json"
    exit /b 0
)
if exist "config\tables.json" (
    set "TABLE_CONFIG_PATH=config\tables.json"
    call :log_info "Found table configuration: config\tables.json"
    exit /b 0
)
if exist "backup\tables.json" (
    set "TABLE_CONFIG_PATH=backup\tables.json"
    call :log_info "Found table configuration: backup\tables.json"
    exit /b 0
)

call :log_error "No table configuration file found"
exit /b 1

:validate_table_config
set "config_file=%~1"
if not exist "%config_file%" (
    call :log_error "Configuration file not found: %config_file%"
    exit /b 1
)
call :log_info "Configuration validation completed"
goto :eof

:check_python_available
%PYTHON_CMD% --version >nul 2>&1
if !ERRORLEVEL! NEQ 0 (
    call :log_error "Python not available or not in PATH"
    exit /b 1
)
goto :eof

:check_disk_space
%PYTHON_CMD% -c "import shutil; free = shutil.disk_usage('.').free / (1024**3); print(f'Free space: {free:.1f} GB'); exit(0 if free > 5 else 1)" 2>nul
if !ERRORLEVEL! NEQ 0 (
    call :log_warning "Low disk space detected (less than 5GB free)"
)
goto :eof

:validate_file_path
set "file_path=%~1"
if not exist "%file_path%" (
    call :log_error "File not found: %~2 (%file_path%)"
    exit /b 1
)
goto :eof

:safe_execute_with_retry
set "command=%~1"
set "context=%~2"
set "max_attempts=%~3"
if "%max_attempts%"=="" set "max_attempts=3"

call :log_info "Executing: %command%"
%command%
set "exit_code=!ERRORLEVEL!"
if !exit_code! NEQ 0 (
    call :log_error "Command failed: %context%"
)
exit /b !exit_code!

:show_recent_logs
if exist "!LOG_FILE!" (
    echo Recent log entries:
    powershell -Command "Get-Content '!LOG_FILE!' | Select-Object -Last 10" 2>nul
) else (
    echo No log file available
)
goto :eof

REM Initialize logging with consistent expansion
call :get_timestamp timestamp
call :create_log_directory "monthly" "!timestamp!" log_dir
set "LOG_FILE=!log_dir!\monthly_backup_!timestamp!.log"
call :init_logging "!LOG_FILE!" "MonthlyBackup" %LOG_LEVEL_INFO%

call :log_operation_start "Monthly Backup System"

REM ===============================================================
REM MAIN MONTHLY PROCESS
REM ===============================================================

REM ===============================================================
REM MAIN MONTHLY PROCESS
REM ===============================================================

:main_monthly_process
echo ===============================================================
echo TNGD MONTHLY BACKUP SYSTEM
echo ===============================================================
echo Target: !MONTH_NAME! !YEAR!
echo Mode: Production-Ready with Fixes
echo Log File: !LOG_FILE!
if "!DRY_RUN!"=="true" echo Mode: DRY RUN (validation only)
if "!FORCE_RECOVERY!"=="true" echo Mode: FORCE RECOVERY enabled
echo Current time: %date% %time%
echo ===============================================================
echo.

call :log_system_info

REM Configuration discovery with proper error handling
call :log_info "Starting configuration validation..."
call :find_table_config TABLE_CONFIG_PATH
if !ERRORLEVEL! NEQ 0 (
    call :handle_error %ERROR_CONFIG% "Table configuration not found"
    exit /b %ERROR_CONFIG%
)

call :validate_table_config "!TABLE_CONFIG_PATH!"
if !ERRORLEVEL! NEQ 0 (
    call :handle_error %ERROR_CONFIG% "Table configuration validation failed"
    exit /b %ERROR_CONFIG%
)

call :log_info "Using table configuration: !TABLE_CONFIG_PATH!"

REM Health checks with proper error handling
if not "!SKIP_HEALTH_CHECKS!"=="true" (
    call :log_info "Starting system health checks..."
    echo Performing system health checks...

    REM Check Python availability
    call :safe_execute "call :check_python_available" "Python availability check"
    if !ERRORLEVEL! NEQ 0 (
        call :handle_error %ERROR_PYTHON% "Python not available or not in PATH"
        exit /b %ERROR_PYTHON%
    )
    echo ✅ Python availability check passed

    REM Check disk space
    call :log_info "Checking disk space..."
    call :safe_execute "call :check_disk_space 5" "Disk space check"
    if !ERRORLEVEL! NEQ 0 (
        call :log_warning "Low disk space detected (less than 5GB free)"
        REM Continue with backup but log the warning
    ) else (
        echo ✅ Disk space check passed
    )

    echo ✅ All health checks completed
    call :log_info "All health checks completed"
) else (
    echo ⚠️ Health checks skipped (--skip-health-checks flag used)
    call :log_info "Health checks skipped by user"
)

echo.
echo ===============================================================
echo STARTING MONTHLY BACKUP PROCESS
echo ===============================================================
echo Configuration: !TABLE_CONFIG_PATH!
echo Features: Error Recovery, Health Monitoring, Input Validation
echo Target: !MONTH_NAME! !YEAR!
echo ===============================================================
echo.

REM Validate backup script exists
call :validate_file_path "!MONTHLY_BACKUP_SCRIPT!" "Monthly backup script"
if !ERRORLEVEL! NEQ 0 (
    call :handle_error %ERROR_FILE_NOT_FOUND% "Monthly backup script not found: !MONTHLY_BACKUP_SCRIPT!"
    exit /b %ERROR_FILE_NOT_FOUND%
)

REM Build parameters with proper validation
set "backup_params=--month "!MONTH_NAME!" --year !YEAR! --config-path "!TABLE_CONFIG_PATH!""
if "!DRY_RUN!"=="true" set "backup_params=!backup_params! --dry-run"
if "!VERBOSE!"=="true" set "backup_params=!backup_params! --verbose"
if "!FORCE_RECOVERY!"=="true" set "backup_params=!backup_params! --force-recovery"

call :log_info "Starting monthly backup processor..."
call :log_info "Running: !PYTHON_CMD! !MONTHLY_BACKUP_SCRIPT! !backup_params!"
echo.

REM Execute backup with proper error handling
call :safe_execute_with_retry "!PYTHON_CMD! !MONTHLY_BACKUP_SCRIPT! !backup_params!" "Monthly backup execution" 3
set "BACKUP_EXIT_CODE=!ERRORLEVEL!"

echo.
echo ===============================================================
echo MONTHLY BACKUP COMPLETED
echo ===============================================================
echo Exit code: !BACKUP_EXIT_CODE!
echo Completion time: %date% %time%
echo Log file: !LOG_FILE!
echo ===============================================================

REM Generate completion summary
call :log_operation_end "Monthly Backup System" !BACKUP_EXIT_CODE!

if !BACKUP_EXIT_CODE! EQU 0 (
    echo ✅ Monthly backup completed successfully!
    call :log_info "Monthly backup completed successfully"
) else (
    echo ❌ Monthly backup failed or completed with errors.
    echo Check the log file for details: !LOG_FILE!
    call :log_error "Monthly backup failed - check logs for details"

    REM Generate error report
    call :generate_error_report
)

echo.
echo Recent log entries:
echo ===============================================================
call :show_recent_logs 10
echo ===============================================================

exit /b !BACKUP_EXIT_CODE!

:show_monthly_help
echo ===============================================================
echo TNGD Monthly Backup System - Fixed Version
echo ===============================================================
echo.
echo FIXES IMPLEMENTED:
echo   ✅ Consistent delayed expansion usage
echo   ✅ Proper input validation and security hardening
echo   ✅ Standardized error handling with retry mechanisms
echo   ✅ Multiple configuration file fallbacks
echo   ✅ Comprehensive system health monitoring
echo   ✅ Centralized logging and configuration management
echo.
echo Usage:
echo   run_monthly_backup_enhanced.bat [month] [year] [options]
echo.
echo Parameters:
echo   month                  Month name (required, e.g., march, april)
echo   year                   Year (optional, default: 2025)
echo.
echo Options:
echo   --dry-run              Validate only, no backup
echo   --verbose              Enable verbose logging
echo   --force-recovery       Force recovery mode for failed operations
echo   --skip-health-checks   Skip system health checks (not recommended)
echo.
echo Examples:
echo   run_monthly_backup_enhanced.bat march 2025
echo   run_monthly_backup_enhanced.bat march 2025 --dry-run
echo   run_monthly_backup_enhanced.bat march 2025 --verbose --force-recovery
echo.
echo Configuration Discovery Order:
echo   1. tabletest\tables.json (primary)
echo   2. config\tables.json (fallback)
echo   3. backup\tables.json (secondary fallback)
echo   4. config\backup_tables.json (tertiary fallback)
echo.
echo Security Features:
echo   • Input validation for all parameters
echo   • Safe string validation to prevent injection
echo   • Proper error handling with detailed logging
echo   • Comprehensive health checks before execution
echo.
echo ===============================================================
exit /b 0
