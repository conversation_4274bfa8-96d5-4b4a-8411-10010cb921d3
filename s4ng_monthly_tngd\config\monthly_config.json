{"monthly_backup": {"devo": {"base_url": "https://api.devo.com", "credentials_file": "config/devo_credentials.json", "timeout_seconds": 300, "max_retries": 3}, "storage": {"type": "oss", "bucket": "devo-backups", "path_template": "monthly/{year}/{month:02d}/{table_name}_{year}_{month:02d}_{day:02d}.tar.gz", "compression": "gzip", "verify_uploads": true}, "processing": {"chunk_size": 10000, "max_retries": 3, "retry_delay_seconds": 60, "timeout_seconds": 3600, "max_concurrent_tables": 2, "memory_limit_mb": 2048}, "validation": {"verify_row_counts": true, "verify_data_integrity": true, "skip_empty_days": false, "validate_historical_dates_only": true}, "recovery": {"enable_checkpoints": true, "checkpoint_interval_minutes": 15, "auto_resume": true, "max_resume_attempts": 3}}}