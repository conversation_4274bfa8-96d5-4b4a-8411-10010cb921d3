@echo off
REM ===============================================================
REM MARCH 2025 BACKUP UTILITY
REM ===============================================================
REM
REM This script provides a simple interface to backup all tables
REM for March 2025 using the working backup implementation.
REM
REM Usage:
REM   bin\backup_march_2025.bat [options]
REM
REM Options:
REM   --dry-run          Test mode - validate only, no backup
REM   --verbose          Enable detailed logging
REM   --table-limit N    Limit to first N tables (for testing)
REM   --help             Show this help message
REM
REM Examples:
REM   bin\backup_march_2025.bat --dry-run
REM   bin\backup_march_2025.bat --verbose
REM   bin\backup_march_2025.bat --table-limit 5 --verbose
REM   bin\backup_march_2025.bat
REM
REM ===============================================================

setlocal enabledelayedexpansion

REM Set the current directory to the project root
cd /d "%~dp0.."

REM Configuration
set "PYTHON_CMD=python"
set "BACKUP_SCRIPT=scripts\backup_march_2025.py"

REM Check for help
if /i "%~1"=="--help" goto show_help
if /i "%~1"=="help" goto show_help

REM Display header
echo.
echo ===============================================================
echo 🗓️  MARCH 2025 BACKUP UTILITY
echo ===============================================================
echo.
echo This utility will backup all tables for March 2025 (2025-03-01 to 2025-03-31)
echo using the working backup implementation that actually queries Devo and uploads to OSS.
echo.

REM Check if script exists
if not exist "%BACKUP_SCRIPT%" (
    echo ❌ ERROR: Backup script not found: %BACKUP_SCRIPT%
    echo.
    pause
    exit /b 1
)

REM Build command with all arguments
set "backup_command=%PYTHON_CMD% %BACKUP_SCRIPT%"
if not "%*"=="" set "backup_command=%backup_command% %*"

echo 🚀 Running: %backup_command%
echo.

REM Execute the backup
%backup_command%
set "exit_code=%ERRORLEVEL%"

echo.
echo ===============================================================
if %exit_code% EQU 0 (
    echo ✅ BACKUP COMPLETED SUCCESSFULLY
) else (
    echo ❌ BACKUP FAILED WITH EXIT CODE: %exit_code%
)
echo ===============================================================
echo.

REM Show next steps
if %exit_code% EQU 0 (
    echo 📋 NEXT STEPS:
    echo.
    echo 1. Check the logs above for backup summary
    echo 2. Verify files were uploaded to OSS at: Devo/March/march_2025_backup/
    echo 3. You can run this script again for other months by modifying the dates
    echo.
    echo 💡 TIP: Use --dry-run first to test before running actual backup
    echo.
) else (
    echo 🔧 TROUBLESHOOTING:
    echo.
    echo 1. Check the error messages above
    echo 2. Verify Devo and OSS credentials are configured
    echo 3. Try running with --dry-run first
    echo 4. Use --table-limit 1 to test with a single table
    echo.
)

pause
exit /b %exit_code%

:show_help
echo.
echo ===============================================================
echo MARCH 2025 BACKUP UTILITY - HELP
echo ===============================================================
echo.
echo This utility backs up all tables for March 2025 using a working
echo backup implementation that actually queries Devo and uploads to OSS.
echo.
echo USAGE:
echo   bin\backup_march_2025.bat [options]
echo.
echo OPTIONS:
echo   --dry-run          Test mode - validate only, no backup
echo   --verbose          Enable detailed logging
echo   --table-limit N    Limit to first N tables (for testing)
echo   --start-date DATE  Start date (YYYY-MM-DD, default: 2025-03-01)
echo   --end-date DATE    End date (YYYY-MM-DD, default: 2025-03-31)
echo   --help             Show this help message
echo.
echo EXAMPLES:
echo   bin\backup_march_2025.bat --dry-run
echo   bin\backup_march_2025.bat --verbose
echo   bin\backup_march_2025.bat --table-limit 5 --verbose
echo   bin\backup_march_2025.bat --start-date 2025-03-01 --end-date 2025-03-15
echo   bin\backup_march_2025.bat
echo.
echo FEATURES:
echo   ✅ Actually queries Devo (not simulation)
echo   ✅ Actually uploads to OSS (not simulation)
echo   ✅ Processes all 63 tables in one run
echo   ✅ Handles date ranges efficiently
echo   ✅ Comprehensive error handling
echo   ✅ Detailed logging and progress tracking
echo.
echo FILES WILL BE UPLOADED TO:
echo   OSS Path: Devo/March/march_2025_backup/[table_name]_march_2025.tar.gz
echo.
echo ===============================================================
echo.
pause
exit /b 0
