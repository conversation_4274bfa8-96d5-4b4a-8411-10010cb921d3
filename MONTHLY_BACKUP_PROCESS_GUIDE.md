# Monthly Backup Process - Complete Guide

## 🎯 **PROJECT OVERVIEW**

This guide outlines the complete process for creating a dedicated, production-ready monthly backup system that addresses all known issues and provides a clean, focused architecture.

---

## 🚨 **CRITICAL ISSUES IDENTIFIED**

### Current System Problems:
1. **Date Bug**: System backs up current date (2025-06-24) instead of specified historical date (2025-03-01)
2. **Shared Library Loading**: 'batch label not found' errors when shared functions aren't loaded properly
3. **Architecture Confusion**: Mixed daily/monthly processes causing system confusion
4. **Simulation Mode**: Current system runs in simulation mode, not actual backup

---

## 🏗️ **RECOMMENDED ARCHITECTURE: DEDICATED MONTHLY BACKUP PROJECT**

### Why Separate Project?
- **Clear Separation**: Eliminates confusion between daily and monthly processes
- **Focused Functionality**: Only monthly backup features, no unused code
- **Easier Maintenance**: Simpler debugging and updates
- **Production Ready**: Clean, focused implementation

### Project Structure:
```
monthly-backup-system/
├── README.md
├── requirements.txt
├── config/
│   ├── monthly_config.json
│   └── tables.json
├── src/
│   ├── __init__.py
│   ├── monthly_backup.py
│   ├── devo_client.py
│   ├── storage_manager.py
│   └── config_manager.py
├── scripts/
│   └── run_monthly_backup.py
├── logs/
├── tests/
└── docs/
```

---

## 📋 **STEP-BY-STEP IMPLEMENTATION PROCESS**

### Phase 1: Project Setup (Day 1)
1. **Create New Project Directory**
   ```bash
   mkdir monthly-backup-system
   cd monthly-backup-system
   ```

2. **Initialize Python Environment**
   ```bash
   python -m venv venv
   venv\Scripts\activate  # Windows
   pip install -r requirements.txt
   ```

3. **Create Core Configuration**
   - Monthly-specific configuration file
   - Table definitions
   - Environment variables

### Phase 2: Core Implementation (Days 2-3)
1. **Implement Date Handling (CRITICAL FIX)**
   ```python
   # Ensure historical date processing
   def get_target_date(year: int, month: int, day: int) -> datetime.date:
       """Get the exact historical date - NOT current date"""
       target_date = datetime.date(year, month, day)
       
       # CRITICAL: Validate we're not using current date
       current_date = datetime.date.today()
       if target_date >= current_date:
           raise ValueError(f"Target date {target_date} cannot be current or future date")
       
       return target_date
   ```

2. **Implement Devo Client**
   - Historical data querying
   - Date range validation
   - Error handling and retries

3. **Implement Storage Manager**
   - OSS upload functionality
   - Compression handling
   - Path management

### Phase 3: Integration & Testing (Days 4-5)
1. **Create Main Backup Script**
2. **Implement Comprehensive Testing**
3. **Add Logging and Monitoring**
4. **Error Recovery Mechanisms**

### Phase 4: Production Deployment (Day 6)
1. **Security Hardening**
2. **Performance Optimization**
3. **Documentation**
4. **Deployment Scripts**

---

## 🔧 **TECHNICAL SPECIFICATIONS**

### Core Features:
- **Historical Date Processing**: Ensures backup of specified historical dates only
- **Robust Error Handling**: Comprehensive retry mechanisms
- **Progress Tracking**: Real-time progress monitoring
- **Checkpoint Recovery**: Resume from interruptions
- **Validation**: Data integrity checks
- **Security**: Input validation and secure credential handling

### Configuration Example:
```json
{
  "monthly_backup": {
    "target_month": 3,
    "target_year": 2025,
    "tables": ["my.app.tngd.waf", "my.app.tngd.dns"],
    "storage": {
      "oss_bucket": "devo-backups",
      "path_prefix": "monthly/{year}/{month:02d}/"
    },
    "processing": {
      "chunk_size": 10000,
      "max_retries": 3,
      "timeout_seconds": 3600
    }
  }
}
```

---

## 🚀 **EXECUTION WORKFLOW**

### 1. Pre-Execution Validation
```bash
# Validate configuration
python scripts/run_monthly_backup.py --validate-config

# Dry run test
python scripts/run_monthly_backup.py --month 3 --year 2025 --dry-run
```

### 2. Production Execution
```bash
# Full monthly backup
python scripts/run_monthly_backup.py --month 3 --year 2025 --verbose

# With specific tables
python scripts/run_monthly_backup.py --month 3 --year 2025 --tables "my.app.tngd.waf,my.app.tngd.dns"
```

### 3. Monitoring & Recovery
```bash
# Check progress
python scripts/run_monthly_backup.py --status

# Resume from checkpoint
python scripts/run_monthly_backup.py --resume --checkpoint-id "backup_20250301_123456"
```

---

## 📊 **QUALITY ASSURANCE**

### Testing Strategy:
1. **Unit Tests**: Individual component testing
2. **Integration Tests**: End-to-end workflow testing
3. **Performance Tests**: Large dataset handling
4. **Security Tests**: Input validation and injection prevention
5. **Recovery Tests**: Checkpoint and resume functionality

### Validation Checklist:
- [ ] Historical dates processed correctly (NOT current date)
- [ ] All tables backed up successfully
- [ ] Data integrity verified
- [ ] Storage paths correct
- [ ] Error handling works
- [ ] Recovery mechanisms functional
- [ ] Logging comprehensive
- [ ] Security measures in place

---

## 🔒 **SECURITY CONSIDERATIONS**

### Data Protection:
- Encrypted credential storage
- Secure API communication
- Input validation and sanitization
- Access logging and monitoring

### Operational Security:
- Principle of least privilege
- Secure configuration management
- Audit trail maintenance
- Incident response procedures

---

## 📈 **SUCCESS METRICS**

### Key Performance Indicators:
- **Reliability**: 99.9% successful backup completion
- **Performance**: Process 1M records per hour
- **Recovery**: Resume within 5 minutes of interruption
- **Accuracy**: 100% data integrity validation
- **Security**: Zero security incidents

---

## 🎯 **NEXT STEPS**

1. **Immediate**: Create new dedicated project structure
2. **Priority 1**: Fix date handling bug (historical vs current date)
3. **Priority 2**: Implement robust error handling
4. **Priority 3**: Add comprehensive testing
5. **Priority 4**: Production deployment and monitoring

---

## 📞 **SUPPORT & MAINTENANCE**

### Regular Maintenance:
- Monthly configuration reviews
- Quarterly performance assessments
- Annual security audits
- Continuous monitoring and alerting

### Troubleshooting:
- Comprehensive logging for issue diagnosis
- Automated error reporting
- Recovery procedures documentation
- Support escalation procedures

---

## 💻 **IMPLEMENTATION EXAMPLES**

### Critical Date Fix Implementation:
```python
import datetime
from typing import List, Dict, Any
import logging

class MonthlyBackupProcessor:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)

    def validate_target_date(self, year: int, month: int, day: int) -> datetime.date:
        """
        CRITICAL FIX: Ensure we process historical dates, not current date
        """
        try:
            target_date = datetime.date(year, month, day)
            current_date = datetime.date.today()

            # CRITICAL: Prevent backing up current/future dates
            if target_date >= current_date:
                raise ValueError(
                    f"Invalid target date {target_date}. "
                    f"Cannot backup current ({current_date}) or future dates."
                )

            self.logger.info(f"Validated target date: {target_date}")
            return target_date

        except ValueError as e:
            self.logger.error(f"Date validation failed: {e}")
            raise

    def process_month(self, year: int, month: int) -> Dict[str, Any]:
        """Process entire month with proper date handling"""
        import calendar

        days_in_month = calendar.monthrange(year, month)[1]
        results = {
            'year': year,
            'month': month,
            'days_processed': 0,
            'days_successful': 0,
            'errors': []
        }

        for day in range(1, days_in_month + 1):
            try:
                # CRITICAL: Validate each date before processing
                target_date = self.validate_target_date(year, month, day)

                # Process this specific historical date
                day_result = self.process_single_day(target_date)
                results['days_processed'] += 1

                if day_result['success']:
                    results['days_successful'] += 1
                else:
                    results['errors'].append({
                        'date': str(target_date),
                        'error': day_result['error']
                    })

            except Exception as e:
                self.logger.error(f"Failed to process {year}-{month:02d}-{day:02d}: {e}")
                results['errors'].append({
                    'date': f"{year}-{month:02d}-{day:02d}",
                    'error': str(e)
                })

        return results
```

### Shared Library Loading Fix:
```python
import os
import sys
from pathlib import Path

class SharedLibraryManager:
    """Handles proper loading of shared functions and libraries"""

    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.shared_libs = {}

    def load_shared_functions(self) -> bool:
        """
        CRITICAL FIX: Ensure all shared functions are properly loaded
        """
        try:
            # Add project root to Python path
            sys.path.insert(0, str(self.project_root))

            # Import required modules
            from utils.minimal_logging import logger
            from core.config_manager import ConfigManager
            from core.devo_client import DevoClient
            from core.storage_manager import StorageManager

            self.shared_libs = {
                'logger': logger,
                'config_manager': ConfigManager,
                'devo_client': DevoClient,
                'storage_manager': StorageManager
            }

            logger.info("All shared libraries loaded successfully")
            return True

        except ImportError as e:
            print(f"CRITICAL ERROR: Failed to load shared libraries: {e}")
            return False

    def get_library(self, name: str):
        """Get a loaded shared library"""
        if name not in self.shared_libs:
            raise RuntimeError(f"Library '{name}' not loaded. Call load_shared_functions() first.")
        return self.shared_libs[name]
```

### Production-Ready Main Script:
```python
#!/usr/bin/env python3
"""
Monthly Backup System - Production Ready
Addresses all critical issues from the original system
"""

import argparse
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any, List

def setup_logging(verbose: bool = False) -> logging.Logger:
    """Setup comprehensive logging"""
    log_level = logging.DEBUG if verbose else logging.INFO

    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(f'logs/monthly_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
            logging.StreamHandler(sys.stdout)
        ]
    )

    return logging.getLogger(__name__)

def main():
    parser = argparse.ArgumentParser(description='Monthly Backup System - Production Ready')
    parser.add_argument('--month', type=int, required=True, help='Target month (1-12)')
    parser.add_argument('--year', type=int, required=True, help='Target year')
    parser.add_argument('--config', default='config/monthly_config.json', help='Configuration file')
    parser.add_argument('--dry-run', action='store_true', help='Validate only, no actual backup')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    parser.add_argument('--tables', help='Comma-separated list of specific tables to backup')

    args = parser.parse_args()

    # Setup logging
    logger = setup_logging(args.verbose)
    logger.info("=" * 60)
    logger.info("MONTHLY BACKUP SYSTEM - PRODUCTION READY")
    logger.info("=" * 60)

    try:
        # Load shared libraries
        lib_manager = SharedLibraryManager(Path.cwd())
        if not lib_manager.load_shared_functions():
            logger.error("Failed to load shared libraries")
            sys.exit(1)

        # Initialize processor
        processor = MonthlyBackupProcessor(config)

        # CRITICAL: Validate target month/year before processing
        if not processor.validate_target_month(args.year, args.month):
            logger.error(f"Invalid target month/year: {args.year}-{args.month}")
            sys.exit(1)

        # Process monthly backup
        if args.dry_run:
            logger.info("DRY RUN MODE - No actual backup will be performed")
            result = processor.validate_configuration(args.year, args.month)
        else:
            logger.info("PRODUCTION MODE - Actual backup will be performed")
            result = processor.process_month(args.year, args.month)

        # Report results
        logger.info("=" * 60)
        logger.info("BACKUP COMPLETED")
        logger.info("=" * 60)
        logger.info(f"Status: {result.get('status', 'unknown')}")
        logger.info(f"Days processed: {result.get('days_processed', 0)}")
        logger.info(f"Days successful: {result.get('days_successful', 0)}")

        if result.get('errors'):
            logger.warning(f"Errors encountered: {len(result['errors'])}")
            for error in result['errors']:
                logger.error(f"  {error['date']}: {error['error']}")

        # Exit with appropriate code
        success = result.get('status') == 'completed' and not result.get('errors')
        sys.exit(0 if success else 1)

    except Exception as e:
        logger.error(f"CRITICAL ERROR: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
```

---

*This guide provides the foundation for a production-ready monthly backup system that addresses all current issues and provides a clean, maintainable architecture.*
