#!/usr/bin/env python3
"""
March 2025 Backup Script

This script performs a complete backup of all tables for March 2025 using the working
DevoClient and StorageManager components. It bypasses the simulation processors and
performs actual backups.

Usage:
    python scripts/backup_march_2025.py [options]

Options:
    --dry-run          Validate only, no actual backup
    --verbose          Enable verbose logging
    --table-limit N    Limit to first N tables (for testing)
    --start-date DATE  Start date (YYYY-MM-DD, default: 2025-03-01)
    --end-date DATE    End date (YYYY-MM-DD, default: 2025-03-31)
"""

import os
import sys
import argparse
import datetime
import json
import tempfile
import tarfile
from pathlib import Path
from typing import List, Dict, Any, Optional

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.config_manager import ConfigManager
from core.devo_client import DevoClient
from core.storage_manager import StorageManager
from utils.minimal_logging import logger

class March2025BackupProcessor:
    """Backup processor specifically for March 2025 data."""
    
    def __init__(self):
        """Initialize the backup processor."""
        self.config_manager = ConfigManager()
        self.devo_client = DevoClient()
        self.storage_manager = StorageManager(self.config_manager)
        self.stats = {
            'tables_processed': 0,
            'tables_successful': 0,
            'tables_failed': 0,
            'total_rows': 0,
            'total_files_uploaded': 0,
            'start_time': datetime.datetime.now()
        }
        
        logger.info("March 2025 Backup Processor initialized")
    
    def load_table_names(self) -> List[str]:
        """Load table names from configuration."""
        try:
            config_path = "tabletest/tables.json"
            if not os.path.exists(config_path):
                raise FileNotFoundError(f"Table configuration not found: {config_path}")

            with open(config_path, 'r') as f:
                table_names = json.load(f)

            # Handle both list format and object format
            if isinstance(table_names, list):
                # Simple list format
                logger.info(f"Loaded {len(table_names)} tables from configuration (list format)")
                return table_names
            elif isinstance(table_names, dict) and 'tables' in table_names:
                # Object format with tables array
                tables = []
                for table_config in table_names['tables']:
                    if isinstance(table_config, str):
                        tables.append(table_config)
                    elif isinstance(table_config, dict) and table_config.get('enabled', True):
                        tables.append(table_config['name'])
                logger.info(f"Loaded {len(tables)} tables from configuration (object format)")
                return tables
            else:
                raise ValueError("Invalid table configuration format")

        except Exception as e:
            logger.error(f"Failed to load table names: {str(e)}")
            raise
    
    def backup_march_2025(self, dry_run: bool = False, verbose: bool = False, 
                         table_limit: Optional[int] = None,
                         start_date: str = "2025-03-01", 
                         end_date: str = "2025-03-31") -> Dict[str, Any]:
        """
        Backup all tables for March 2025.
        
        Args:
            dry_run: If True, validate only
            verbose: Enable verbose logging
            table_limit: Limit to first N tables
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            
        Returns:
            Backup results dictionary
        """
        logger.info("=" * 60)
        logger.info("MARCH 2025 BACKUP PROCESSOR - STARTING")
        logger.info("=" * 60)
        logger.info(f"Date Range: {start_date} to {end_date}")
        logger.info(f"Mode: {'DRY RUN' if dry_run else 'PRODUCTION'}")
        logger.info(f"Start Time: {self.stats['start_time']}")
        
        try:
            # Load table names
            table_names = self.load_table_names()
            
            # Apply table limit if specified
            if table_limit:
                table_names = table_names[:table_limit]
                logger.info(f"Limited to first {table_limit} tables")
            
            logger.info(f"Processing {len(table_names)} tables")
            
            # Process each table
            for table_name in table_names:
                try:
                    logger.info(f"Processing table: {table_name}")
                    self.stats['tables_processed'] += 1
                    
                    if dry_run:
                        # Dry run - just validate
                        logger.info(f"DRY RUN: Would backup {table_name} for {start_date} to {end_date}")
                        self.stats['tables_successful'] += 1
                    else:
                        # Actual backup
                        success = self._backup_table_date_range(
                            table_name, start_date, end_date, verbose
                        )
                        
                        if success:
                            self.stats['tables_successful'] += 1
                            logger.info(f"✅ Successfully backed up {table_name}")
                        else:
                            self.stats['tables_failed'] += 1
                            logger.error(f"❌ Failed to backup {table_name}")
                            
                except Exception as e:
                    self.stats['tables_failed'] += 1
                    logger.error(f"❌ Error processing table {table_name}: {str(e)}")
            
            # Calculate final stats
            self.stats['end_time'] = datetime.datetime.now()
            self.stats['duration'] = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
            
            # Generate summary
            self._print_summary()
            
            return {
                'status': 'success' if self.stats['tables_failed'] == 0 else 'partial',
                'stats': self.stats,
                'dry_run': dry_run
            }
            
        except Exception as e:
            logger.error(f"Critical error in March 2025 backup: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'stats': self.stats
            }
    
    def _backup_table_date_range(self, table_name: str, start_date: str, 
                                end_date: str, verbose: bool = False) -> bool:
        """
        Backup a single table for the specified date range.
        
        Args:
            table_name: Name of the table to backup
            start_date: Start date (YYYY-MM-DD)
            end_date: End date (YYYY-MM-DD)
            verbose: Enable verbose logging
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Build query for the date range
            query = f"""
            from {table_name}
            where eventdate >= '{start_date}' and eventdate <= '{end_date}'
            select *
            """
            
            if verbose:
                logger.info(f"Executing query for {table_name}: {query}")
            
            # Query data from Devo
            logger.info(f"Querying data for {table_name} from {start_date} to {end_date}")
            results = self.devo_client.execute_query(
                query=query,
                table_name=table_name,
                timeout=3600  # 1 hour timeout for large queries
            )
            
            if not results:
                logger.warning(f"No data found for {table_name} in date range")
                return True  # Not an error, just no data
            
            self.stats['total_rows'] += len(results)
            logger.info(f"Retrieved {len(results)} rows for {table_name}")
            
            # Create temporary file for backup
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
                json.dump(results, temp_file, indent=2, default=str)
                temp_json_path = temp_file.name
            
            # Compress the data
            compressed_path = temp_json_path.replace('.json', '.tar.gz')
            with tarfile.open(compressed_path, 'w:gz') as tar:
                tar.add(temp_json_path, arcname=f"{table_name}_march_2025.json")
            
            # Generate OSS path
            oss_path = f"Devo/March/march_2025_backup/{table_name}_march_2025.tar.gz"
            
            # Upload to OSS
            logger.info(f"Uploading {table_name} to OSS: {oss_path}")
            success, upload_details = self.storage_manager.upload_file(
                local_file=compressed_path,
                oss_path=oss_path
            )
            
            if success:
                self.stats['total_files_uploaded'] += 1
                file_size_mb = upload_details.get('file_size', 0) / (1024 * 1024)
                logger.info(f"✅ Uploaded {table_name}: {file_size_mb:.1f}MB")
            else:
                logger.error(f"❌ Failed to upload {table_name}")
                return False
            
            # Cleanup temporary files
            try:
                os.unlink(temp_json_path)
                os.unlink(compressed_path)
            except:
                pass
            
            return True
            
        except Exception as e:
            logger.error(f"Error backing up {table_name}: {str(e)}")
            return False
    
    def _print_summary(self):
        """Print backup summary."""
        logger.info("=" * 60)
        logger.info("MARCH 2025 BACKUP SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Tables Processed: {self.stats['tables_processed']}")
        logger.info(f"Tables Successful: {self.stats['tables_successful']}")
        logger.info(f"Tables Failed: {self.stats['tables_failed']}")
        logger.info(f"Total Rows: {self.stats['total_rows']:,}")
        logger.info(f"Files Uploaded: {self.stats['total_files_uploaded']}")
        logger.info(f"Duration: {self.stats.get('duration', 0):.1f} seconds")
        logger.info("=" * 60)

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='March 2025 Backup Script')
    parser.add_argument('--dry-run', action='store_true',
                       help='Validate only, no actual backup')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--table-limit', type=int,
                       help='Limit to first N tables (for testing)')
    parser.add_argument('--start-date', default='2025-03-01',
                       help='Start date (YYYY-MM-DD, default: 2025-03-01)')
    parser.add_argument('--end-date', default='2025-03-31',
                       help='End date (YYYY-MM-DD, default: 2025-03-31)')
    
    args = parser.parse_args()
    
    try:
        # Create backup processor
        processor = March2025BackupProcessor()
        
        # Run backup
        result = processor.backup_march_2025(
            dry_run=args.dry_run,
            verbose=args.verbose,
            table_limit=args.table_limit,
            start_date=args.start_date,
            end_date=args.end_date
        )
        
        # Return appropriate exit code
        if result['status'] == 'error':
            return 1
        elif result['status'] == 'partial':
            return 1
        else:
            return 0
            
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
