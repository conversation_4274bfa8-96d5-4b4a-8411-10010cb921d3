"""
Date validation module - CRITICAL FIX for historical date handling
"""
import datetime
from typing import Tuple, List, Optional

class DateValidator:
    """Handles proper date validation for monthly backups"""
    
    @staticmethod
    def validate_historical_date(year: int, month: int, day: int) -> Tuple[bool, str]:
        """
        CRITICAL FIX: Ensure only historical dates are processed
        
        Args:
            year: Target year
            month: Target month (1-12)
            day: Target day
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            target_date = datetime.date(year, month, day)
            current_date = datetime.date.today()
            
            # CRITICAL: Prevent current/future date backup
            if target_date >= current_date:
                return False, f"Cannot backup current/future date: {target_date}"
            
            # Validate reasonable historical range
            min_date = datetime.date(2020, 1, 1)
            if target_date < min_date:
                return False, f"Date too old: {target_date} (minimum: {min_date})"
            
            return True, ""
            
        except ValueError as e:
            return False, f"Invalid date: {e}"
    
    @staticmethod
    def get_month_date_range(year: int, month: int) -> List[datetime.date]:
        """Get all valid historical dates in a month"""
        import calendar
        
        dates = []
        days_in_month = calendar.monthrange(year, month)[1]
        
        for day in range(1, days_in_month + 1):
            is_valid, _ = DateValidator.validate_historical_date(year, month, day)
            if is_valid:
                dates.append(datetime.date(year, month, day))
        
        return dates
    
    @staticmethod
    def format_date_for_query(date: datetime.date) -> str:
        """Format date for Devo queries"""
        return date.strftime("%Y-%m-%d")
